// Univer懒加载工具
import { log } from '@/app/lib/logger';

// 缓存已加载的插件，避免重复加载
const loadedPlugins = new Set<string>();



// 基础功能插件懒加载器
export async function loadBasicPlugins(univerInstance: any) {
  const pluginKey = 'basic-plugins';
  if (loadedPlugins.has(pluginKey)) {
    log.debug('基础功能插件已加载，跳过重复加载');
    return;
  }

  // 检查Univer实例是否有效
  if (!univerInstance || typeof univerInstance.registerPlugin !== 'function') {
    log.error('无效的Univer实例，无法加载基础功能插件');
    return;
  }

  try {
    log.debug('开始加载基础功能插件...');
    
    // 并行加载所有基础插件
    const [
      { UniverSheetsFilterPlugin },
      { UniverSheetsFilterUIPlugin },
      { UniverSheetsSortPlugin },
      { UniverSheetsSortUIPlugin },
      { UniverSheetsConditionalFormattingPlugin },
      { UniverSheetsConditionalFormattingUIPlugin },
      { UniverSheetsTablePlugin },
      { UniverSheetsTableUIPlugin },
    ] = await Promise.all([
      import('@univerjs/sheets-filter'),
      import('@univerjs/sheets-filter-ui'),
      import('@univerjs/sheets-sort'),
      import('@univerjs/sheets-sort-ui'),
      import('@univerjs/sheets-conditional-formatting'),
      import('@univerjs/sheets-conditional-formatting-ui'),
      import('@univerjs/sheets-table'),
      import('@univerjs/sheets-table-ui'),
    ]);

    // 并行加载Facade API
    await Promise.all([
      import('@univerjs/sheets-conditional-formatting/facade'),
      import('@univerjs/sheets-filter/facade'),
      import('@univerjs/sheets-sort/facade'),
      import('@univerjs/sheets-table/facade'),
    ]);

    // 注意：CSS样式已经在主组件中预加载，这里不需要动态加载

    // 语言包已在主组件中预加载，这里不需要额外处理
    log.debug('基础插件语言包已预加载');

    // 注册插件 - 添加错误处理
    const pluginsToRegister = [
      { plugin: UniverSheetsFilterPlugin, name: 'UniverSheetsFilterPlugin' },
      { plugin: UniverSheetsFilterUIPlugin, name: 'UniverSheetsFilterUIPlugin' },
      { plugin: UniverSheetsSortPlugin, name: 'UniverSheetsSortPlugin' },
      { plugin: UniverSheetsSortUIPlugin, name: 'UniverSheetsSortUIPlugin' },
      { plugin: UniverSheetsConditionalFormattingPlugin, name: 'UniverSheetsConditionalFormattingPlugin' },
      { plugin: UniverSheetsConditionalFormattingUIPlugin, name: 'UniverSheetsConditionalFormattingUIPlugin' },
      { plugin: UniverSheetsTablePlugin, name: 'UniverSheetsTablePlugin' },
      { plugin: UniverSheetsTableUIPlugin, name: 'UniverSheetsTableUIPlugin' },
    ];

    for (const { plugin, name } of pluginsToRegister) {
      try {
        univerInstance.registerPlugin(plugin);
        log.debug(`${name} 注册成功`);
      } catch (error) {
        log.warn(`${name} 注册失败:`, error);
      }
    }

    loadedPlugins.add(pluginKey);

    log.debug('基础功能插件加载完成');
  } catch (error) {
    log.warn('基础功能插件加载失败:', error);
  }
}

// 高级功能插件懒加载器
export async function loadPremiumPlugins(univerInstance: any) {
  const pluginKey = 'premium-plugins';
  if (loadedPlugins.has(pluginKey)) {
    log.debug('高级功能插件已加载，跳过重复加载');
    return;
  }

  // 检查Univer实例是否有效
  if (!univerInstance || typeof univerInstance.registerPlugin !== 'function') {
    log.error('无效的Univer实例，无法加载高级功能插件');
    return;
  }

  try {
    log.debug('开始加载高级功能插件...');
    
    // 并行加载所有高级插件
    const [
      { UniverSheetsChartPlugin },
      { UniverSheetsChartUIPlugin },
      { UniverSheetsPivotTablePlugin },
      { UniverSheetsPivotTableUIPlugin },
    ] = await Promise.all([
      import('@univerjs-pro/sheets-chart'),
      import('@univerjs-pro/sheets-chart-ui'),
      import('@univerjs-pro/sheets-pivot'),
      import('@univerjs-pro/sheets-pivot-ui'),
    ]);

    // 并行加载Facade API
    await Promise.all([
      import('@univerjs-pro/sheets-chart-ui/facade'),
      import('@univerjs-pro/sheets-pivot/facade'),
    ]);

    // 高级插件的语言包需要在主组件中预加载（如果需要的话）
    log.debug('高级插件语言包处理跳过（暂不支持Pro版本语言包预加载）');

    // 注册插件 - 添加错误处理
    const pluginsToRegister = [
      { plugin: UniverSheetsChartPlugin, name: 'UniverSheetsChartPlugin' },
      { plugin: UniverSheetsChartUIPlugin, name: 'UniverSheetsChartUIPlugin' },
      { plugin: UniverSheetsPivotTablePlugin, name: 'UniverSheetsPivotTablePlugin' },
      { plugin: UniverSheetsPivotTableUIPlugin, name: 'UniverSheetsPivotTableUIPlugin' },
    ];

    for (const { plugin, name } of pluginsToRegister) {
      try {
        univerInstance.registerPlugin(plugin);
        log.debug(`${name} 注册成功`);
      } catch (error) {
        log.warn(`${name} 注册失败:`, error);
      }
    }

    loadedPlugins.add(pluginKey);

    log.debug('高级功能插件加载完成');
  } catch (error) {
    log.warn('高级功能插件加载失败:', error);
  }
}

// 清理加载状态（用于组件卸载时）
export function clearLoadedPlugins() {
  loadedPlugins.clear();
}

// 检查插件是否已加载
export function isPluginLoaded(pluginKey: string): boolean {
  return loadedPlugins.has(pluginKey);
}

// 预加载策略：根据用户交互预测需要的插件
export function preloadPluginsOnDemand(action: string, univerInstance: any) {
  switch (action) {
    case 'filter':
    case 'sort':
    case 'format':
      // 用户开始使用基础功能，立即加载基础插件
      if (!isPluginLoaded('basic-plugins')) {
        loadBasicPlugins(univerInstance);
      }
      break;
    case 'chart':
    case 'pivot':
      // 用户开始使用高级功能，立即加载高级插件
      if (!isPluginLoaded('premium-plugins')) {
        loadPremiumPlugins(univerInstance);
      }
      break;
  }
}
